using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class Notification : ConcurrentAuditableEntity
{
    public Guid NotificationUid { get; set; }

    [AuditProperty]
    public string Title { get; set; } = null!;

    [AuditProperty]
    public string Body { get; set; } = null!;

    [AuditProperty]
    public string NotificationType { get; set; } = null!;


    public bool IsDeleted { get; set; }
    public Guid OrgUid { get; set; }

    public virtual Org Org { get; set; } = null!;
    public virtual ICollection<EmployeeNotification> EmployeeNotifications { get; set; } = [];
    public virtual ICollection<NotificationTarget> NotificationTargets { get; set; } = [];
}



public class NotificationTypeConstant
{
    public const string PUSH = nameof(PUSH);
    public const string EMAIL = nameof(EMAIL);
}


