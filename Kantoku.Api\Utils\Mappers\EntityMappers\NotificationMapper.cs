using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Notification.Request;
using Kantoku.Api.Dtos.Notification.Response;
using Kantoku.Api.Utils.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class NotificationMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Notification entity to a NotificationResponseDto
    /// </summary>
    public static NotificationResponseDto ToNotificationResponseDto(this Notification notification)
    {
        if (notification == null)
            return new NotificationResponseDto();

        return new NotificationResponseDto
        {
            NotificationId = notification.NotificationUid.ToString(),
            Title = notification.Title,
            Body = notification.Body,
            NotificationType = notification.NotificationType,
            Targets = notification.NotificationTargets?.Select(t => new NotificationTargetResponseDto
            {
                NotificationTargetUid = t.NotificationTargetUid.ToString(),
                TargetType = t.TargetType,
                TargetIds = t.TargetIds,
                PublishStatus = t.PublishStatus,
                PublishAt = t.PublishAt
            }) ?? [],
        };
    }

    /// <summary>
    /// Maps a collection of Notification entities to a NotificationsResponseDto
    /// </summary>
    public static NotificationsResponseDto ToNotificationsResponseDto(
        this IEnumerable<Notification> notifications,
        int pageNum,
        int pageSize,
        int totalRecords)
    {
        if (notifications == null)
            return new NotificationsResponseDto();

        return new NotificationsResponseDto
        {
            Items = notifications.Select(n => n.ToNotificationResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateNotificationRequestDto to a Notification entity
    /// </summary>
    public static Notification? ToEntity(this CreateNotificationRequestDto dto, Guid orgUid)
    {
        if (dto == null)
            return null;

        var entity = new Notification
        {
            NotificationUid = GuidHelper.GenerateUUIDv7(),
            Title = dto.Title,
            Body = dto.Body,
            NotificationType = dto.NotificationType,
            OrgUid = orgUid,
            IsDeleted = false,
        };
        entity.NotificationTargets = dto.Targets.Select(t => new NotificationTarget
        {
            NotificationTargetUid = GuidHelper.GenerateUUIDv7(),
            NotificationUid = entity.NotificationUid,
            TargetType = t.TargetType,
            TargetIds = t.TargetIds,
            PublishStatus = NotificationStatusConstant.PENDING,
            IsDeleted = false
        }).ToList();

        return entity;
    }

    /// <summary>
    /// Updates a Notification entity from an UpdateNotificationRequestDto
    /// </summary>
    public static void UpdateFromDto(this Notification entity, UpdateNotificationRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        // Update notification properties
        if (!string.IsNullOrWhiteSpace(dto.Title))
            entity.Title = dto.Title;

        if (!string.IsNullOrWhiteSpace(dto.Body))
            entity.Body = dto.Body;

        if (!string.IsNullOrWhiteSpace(dto.NotificationType))
            entity.NotificationType = dto.NotificationType;

        // Update notification targets if provided
        if (dto.Targets != null)
        {
            UpdateNotificationTargets(entity, dto.Targets);
        }
    }

    /// <summary>
    /// Updates notification targets with proper handling of add, update, and delete operations
    /// </summary>
    private static void UpdateNotificationTargets(Notification entity, IEnumerable<UpdateNotificationTargetRequestDto> targetDtos)
    {
        var targetDtosList = targetDtos.ToList();

        foreach (var existingTarget in entity.NotificationTargets.Where(t => !t.IsDeleted))
        {
            var matchingDto = targetDtosList.FirstOrDefault(dto =>
                dto.NotificationTargetId.HasValue &&
                dto.NotificationTargetId.Value == existingTarget.NotificationTargetUid);

            if (matchingDto == null)
            {
                // Target not included in update - leave as is
                continue;
            }

            // Handle soft delete
            if (matchingDto.IsDeleted == true)
            {
                existingTarget.IsDeleted = true;
                continue;
            }

            // Only allow updates to targets that haven't been published yet
            if (existingTarget.PublishStatus != NotificationStatusConstant.PENDING)
            {
                continue;
            }

            // Update target properties
            if (!string.IsNullOrWhiteSpace(matchingDto.TargetType))
                existingTarget.TargetType = matchingDto.TargetType;

            if (matchingDto.TargetIds != null)
                existingTarget.TargetIds = matchingDto.TargetIds;
        }

        var newTargets = targetDtosList
            .Where(dto => !dto.NotificationTargetId.HasValue &&
                         !string.IsNullOrWhiteSpace(dto.TargetType))
            .Select(dto => new NotificationTarget
            {
                NotificationTargetUid = GuidHelper.GenerateUUIDv7(),
                NotificationUid = entity.NotificationUid,
                TargetType = dto.TargetType!,
                TargetIds = dto.TargetIds ?? [],
                PublishStatus = NotificationStatusConstant.PENDING,
                IsDeleted = false
            });

        foreach (var newTarget in newTargets)
        {
            entity.NotificationTargets.Add(newTarget);
        }
    }

    #endregion
}