using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Notification.Request;
using Kantoku.Api.Dtos.Notification.Response;
using Kantoku.Api.Utils.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class NotificationMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Notification entity to a NotificationResponseDto
    /// </summary>
    public static NotificationResponseDto ToNotificationResponseDto(this Notification notification)
    {
        if (notification == null)
            return new NotificationResponseDto();

        return new NotificationResponseDto
        {
            NotificationId = notification.NotificationUid.ToString(),
            Title = notification.Title,
            Body = notification.Body,
            NotificationType = notification.NotificationType,
            Targets = notification.NotificationTargets?.Select(t => new NotificationTargetResponseDto
            {
                NotificationTargetUid = t.NotificationTargetUid.ToString(),
                TargetType = t.TargetType,
                TargetIds = t.TargetIds,
                PublishStatus = t.PublishStatus,
                PublishAt = t.PublishAt
            }) ?? [],
        };
    }

    /// <summary>
    /// Maps a collection of Notification entities to a NotificationsResponseDto
    /// </summary>
    public static NotificationsResponseDto ToNotificationsResponseDto(
        this IEnumerable<Notification> notifications,
        int pageNum,
        int pageSize,
        int totalRecords)
    {
        if (notifications == null)
            return new NotificationsResponseDto();

        return new NotificationsResponseDto
        {
            Items = notifications.Select(n => n.ToNotificationResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateNotificationRequestDto to a Notification entity
    /// </summary>
    public static Notification? ToEntity(this CreateNotificationRequestDto dto, Guid orgUid)
    {
        if (dto == null)
            return null;

        var entity = new Notification
        {
            NotificationUid = GuidHelper.GenerateUUIDv7(),
            Title = dto.Title,
            Body = dto.Body,
            NotificationType = dto.NotificationType,
            OrgUid = orgUid,
            IsDeleted = false,
        };
        entity.NotificationTargets = dto.Targets.Select(t => new NotificationTarget
        {
            NotificationTargetUid = GuidHelper.GenerateUUIDv7(),
            NotificationUid = entity.NotificationUid,
            TargetType = t.TargetType,
            TargetIds = t.TargetIds,
            PublishStatus = NotificationStatusConstant.PENDING,
            IsDeleted = false
        }).ToList();

        return entity;
    }

    /// <summary>
    /// Updates a Notification entity from an UpdateNotificationRequestDto
    /// </summary>
    public static void UpdateFromDto(this Notification entity, UpdateNotificationRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.Title != null)
            entity.Title = dto.Title;

        if (dto.Body != null)
            entity.Body = dto.Body;

        if (dto.NotificationType != null)
            entity.NotificationType = dto.NotificationType;

        if (dto.Targets != null)
        {
            foreach (var existTarget in entity.NotificationTargets)
            {
                var match = dto.Targets.FirstOrDefault(dto => dto.NotificationTargetId == existTarget.NotificationTargetUid);
                if (match == null)
                {
                    continue;
                }
                if (match.IsDeleted == true)
                {
                    existTarget.IsDeleted = true;
                    continue;
                }
                if (existTarget.PublishStatus != NotificationStatusConstant.PENDING)
                {
                    continue;
                }
                existTarget.TargetType = match.TargetType ?? "";
                existTarget.TargetIds = match.TargetIds ?? [];
            }

            foreach (var target in dto.Targets)
            {
                if (target.NotificationTargetId == null && !string.IsNullOrEmpty(target.TargetType))
                {
                    var newTarget = new NotificationTarget
                    {
                        NotificationTargetUid = GuidHelper.GenerateUUIDv7(),
                        NotificationUid = entity.NotificationUid,
                        TargetType = target.TargetType!,
                        TargetIds = target.TargetIds ?? [],
                        PublishStatus = NotificationStatusConstant.PENDING,
                    };
                    entity.NotificationTargets.Add(newTarget);
                }
            }
        }
    }

    #endregion
}