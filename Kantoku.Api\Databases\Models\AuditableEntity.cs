namespace Kantoku.Api.Databases.Models;

public interface IAuditableEntity
{
    string? CreatedBy { get; set; }
    DateTime? CreatedTime { get; set; }
    string? LastModifiedBy { get; set; }
    DateTime? LastModifiedTime { get; set; }
}

/// <summary>
/// Interface for entities that need optimistic concurrency control
/// </summary>
public interface IConcurrencyEntity
{
    byte[]? RowVersion { get; set; }
}

public abstract class AuditableEntity : IAuditableEntity
{
    public string? CreatedBy { get; set; }
    public DateTime? CreatedTime { get; set; }
    public string? LastModifiedBy { get; set; }
    public DateTime? LastModifiedTime { get; set; }
}

/// <summary>
/// Base class for entities that need both auditing and concurrency control
/// </summary>
public abstract class ConcurrentAuditableEntity : AuditableEntity, IConcurrencyEntity
{
    public byte[]? RowVersion { get; set; }
}