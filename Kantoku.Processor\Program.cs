using Kantoku.Processor.Configurations;
using Kantoku.Processor.Data.Contexts;
using Kantoku.Processor.Extensions;
using Hangfire;
using Hangfire.PostgreSql;
using Hangfire.Console;
using Kantoku.Processor.Jobs;
using EventBus.Kafka.Configuration;

var builder = WebApplication.CreateBuilder(args);

var env = builder.Environment;
builder.Configuration.AddEnvironmentVariables();
builder.Configuration.AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: false, reloadOnChange: false);

// Configure options
builder.Services.Configure<AppConfig>(builder.Configuration.GetSection("AppConfig"));
builder.Services.Configure<AppDbConfig>(builder.Configuration.GetSection("AppDbConfig"));
builder.Services.Configure<HangfireConfig>(builder.Configuration.GetSection("HangfireConfig"));
builder.Services.Configure<KafkaConsumerConfig>(builder.Configuration.GetSection("KafkaConsumerConfig"));

// Configure application database context
builder.Services.AddDbContext<AppDbContext>(options =>
{
    if (env.IsDevelopment())
    {
        options.EnableDetailedErrors();
        options.EnableSensitiveDataLogging();
    }
});

// Configure services
builder.Services.AddServices();
builder.Services.AddJobs();
builder.Services.AddEventHandlers();
builder.Services.AddHostedServices();

// Configure Hangfire
var hangfireConfig = builder.Configuration.GetSection("HangfireConfig").Get<HangfireConfig>();
builder.Services.AddHangfire(config => config
    .UseSimpleAssemblyNameTypeSerializer()
    .UseRecommendedSerializerSettings()
    .UsePostgreSqlStorage(options => options
        .UseNpgsqlConnection(hangfireConfig!.BuildConnectionString()),
        new PostgreSqlStorageOptions
        {
            SchemaName = hangfireConfig!.Schema
        })
    .UseConsole());

builder.Services.AddHangfireServer(options =>
{
    options.WorkerCount = hangfireConfig!.WorkerCount;
});

// Configure Caching
builder.Services.AddDistributedMemoryCache();

// Add API controllers for job management
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

// Configure middleware
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseStaticFiles(); // Enable static file serving for job management UI

// Configure Hangfire dashboard
if (hangfireConfig!.EnableDashboard)
{
    app.UseHangfireDashboard(hangfireConfig.DashboardPath);
}

app.UseRouting();
app.MapControllers();

// Schedule initial recurring jobs (can be updated via API)
RecurringJob.AddOrUpdate<AutoCheckoutHangfireJob>(
    "auto-checkout-job",
    job => job.ExecuteHangfireJob(null!, CancellationToken.None),
    "0 */1 * * *"); // Every hour

RecurringJob.AddOrUpdate<CheckOutReminderHangfireJob>(
    "checkout-reminder-job",
    job => job.ExecuteHangfireJob(null!, CancellationToken.None),
    "*/5 * * * *"); // Every 5 minutes

RecurringJob.AddOrUpdate<EmployeeCostCalculateHangfireJob>(
    "employee-cost-calculate-job",
    job => job.ExecuteHangfireJob(null!, CancellationToken.None),
    "0 2 1 * *"); // Monthly at 2 AM on the 1st

await app.RunAsync();
