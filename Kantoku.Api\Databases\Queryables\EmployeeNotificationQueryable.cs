using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class EmployeeNotificationQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedNotification { get; set; } = false;
}

public interface IEmployeeNotificationQueryable
{
    IQueryable<EmployeeNotification> GetEmployeeNotificationQuery(
        EmployeeNotificationQueryableOptions options
    );

    IQueryable<EmployeeNotification> GetEmployeeNotificationQueryIncluded(
        EmployeeNotificationQueryableOptions options,
        IQueryable<EmployeeNotification>? query = null
    );

    IQueryable<EmployeeNotification> GetEmployeeNotificationQueryFiltered(
        EmployeeNotificationFilter filter,
        EmployeeNotificationQueryableOptions options
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class EmployeeNotificationQueryable(PostgreDbContext context) :
    BaseQueryable<EmployeeNotification>(context), IEmployeeNotificationQueryable
{
    public IQueryable<EmployeeNotification> GetEmployeeNotificationQuery(
        EmployeeNotificationQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<EmployeeNotification> GetEmployeeNotificationQueryIncluded(
        EmployeeNotificationQueryableOptions options,
        IQueryable<EmployeeNotification>? query = null
    )
    {
        query ??= GetEmployeeNotificationQuery(options);
        if (options.IncludedNotification)
        {
            query = query.Include(p => p.Notification);
        }
        return query;
    }

    public IQueryable<EmployeeNotification> GetEmployeeNotificationQueryFiltered(
        EmployeeNotificationFilter filter,
        EmployeeNotificationQueryableOptions options
    )
    {
        var query = GetEmployeeNotificationQueryIncluded(options);
        if (string.IsNullOrEmpty(filter.Keyword) == false)
        {
            query = query.Where(p => p.Notification.Title.Contains(filter.Keyword));
        }
        if (filter.IsRead != null)
        {
            query = query.Where(p => p.IsRead == filter.IsRead);
        }

        return query;
    }
}
