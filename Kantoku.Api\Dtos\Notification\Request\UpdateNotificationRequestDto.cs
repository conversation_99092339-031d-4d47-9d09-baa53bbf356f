using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.Notification.Request;

public class UpdateNotificationRequestDto
{
    /// <summary>
    /// Title of the notification
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// Body of the notification
    /// </summary>
    public string? Body { get; set; }

    /// <summary>
    /// Type of the notification (PUSH, EMAIL)
    /// </summary>
    public string? NotificationType { get; set; }

    /// <summary>
    /// Targets of the notification
    /// </summary>
    public IEnumerable<UpdateNotificationTargetRequestDto>? Targets { get; set; }
}

public class UpdateNotificationTargetRequestDto
{
    /// <summary>
    /// Notification target id
    /// </summary>
    public Guid? NotificationTargetId { get; set; }

    /// <summary>
    /// Target type of the notification (INDIVIDUAL, ROLE, ALL)
    /// </summary>
    public string? TargetType { get; set; }

    /// <summary>
    /// Target ids of the notification
    /// </summary>
    public List<Guid>? TargetIds { get; set; }

    /// <summary>
    /// Delete status of the notification target
    /// </summary>
    public bool? IsDeleted { get; set; }
}
